package com.hsbc.ssv.bbd.risc;

import com.hsbc.ssv.bbd.risc.bean.ChangeRequest;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class ChangeRequestParser {
    public static List<ChangeRequest> parseChangeRequests(String jsonResponse) {
        List<ChangeRequest> changeRequests = new ArrayList<>();
        JSONObject jsonObject = new JSONObject(jsonResponse);
        JSONArray changesArray = jsonObject.getJSONArray("changes");

        for (int i = 0; i < changesArray.length(); i++) {
            JSONObject changeJson = changesArray.getJSONObject(i);
            ChangeRequest changeRequest = buildChangeRequest(changeJson);

            changeRequests.add(changeRequest);
        }

        return changeRequests;
    }

    private static String limitString(String value, int maxLength) {
        if (value == null) return "";
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }

    public static ChangeRequest buildChangeRequest(JSONObject changeJson) {
        ChangeRequest changeRequest = new ChangeRequest();

        changeRequest.setGsdScheduledStartDate(limitString(changeJson.optString("gsdScheduledStartDate", ""), 1024));
        changeRequest.setGsdBackoutPlan(limitString(changeJson.optString("gsdBackoutPlan", ""), 1024));
        changeRequest.setGsdFullyApprovedDate(limitString(changeJson.optString("gsdFullyApprovedDate", ""), 1024));
        changeRequest.setGsdClosureCode(limitString(changeJson.optString("gsdClosureCode", ""), 1024));
        changeRequest.setGsdAppIDs(changeJson.optJSONArray("gsdAppIDs") != null
                ? changeJson.optJSONArray("gsdAppIDs").toList()
                : new ArrayList<>());

        changeRequest.setGsdRequiresPar(changeJson.optBoolean("gsdRequiresPar", false));
        changeRequest.setSnCrNumber(limitString(changeJson.optString("snCrNumber", ""), 1024));
        changeRequest.setTechnicalImpact(limitString(changeJson.optString("gsdTechnicalImpact", ""), 1024));
        changeRequest.setGsdIsProduction(changeJson.optBoolean("gsdIsProduction", false));
        changeRequest.setGsdScheduledEndDate(limitString(changeJson.optString("gsdScheduledEndDate", ""), 1024));
        changeRequest.setGsdChangeType(limitString(changeJson.optString("gsdChangeType", ""), 1024));
        changeRequest.setGsdBusinessImpact(limitString(changeJson.optString("gsdBusinessImpact", ""), 1024));
        changeRequest.setGsdImplementationPlan(limitString(changeJson.optString("gsdImplementationPlan", ""), 1024));
        changeRequest.setGsdTitle(limitString(changeJson.optString("gsdTitle", ""), 1024));
        changeRequest.setCRStatus(limitString(changeJson.optString("gsdStatus", ""), 1024));
        changeRequest.setDescription(limitString(changeJson.optString("gsdDescription", ""), 1024));
        changeRequest.setPVT(limitString(changeJson.optString("gsdTestPlan", ""), 1024));
        changeRequest.setArtifacts(changeJson.optJSONObject("artifacts") != null
                ? limitString(changeJson.optJSONObject("artifacts").toString(), 256)
                : "");
        changeRequest.setGsdAssigneeUserStaffID(limitString(changeJson.optString("gsdAssigneeUserStaffID", ""), 1024));
        return changeRequest;
    }
}
