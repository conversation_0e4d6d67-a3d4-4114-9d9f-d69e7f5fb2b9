package com.hsbc.ssv.bbd.risc.service;

import com.hsbc.ssv.bbd.risc.ChangeRequestParser;
import com.hsbc.ssv.bbd.risc.DifyWorkflowRunner;
import com.hsbc.ssv.bbd.risc.bean.ChangeRequest;
import com.hsbc.ssv.bbd.risc.util.EncryptionUtil;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.ArrayList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class ICEService {

    private static final Logger logger = LoggerFactory.getLogger(ICEService.class);

    @Value("${ice.api.username}")
    private String username;

    @Value("${ice.api.password}")
    private String encryptedPassword;

    @Value("${dify.cr.quality.api}")
    private String encryptedApiKey;

    @Value("${ice.api.base-url}")
    private String baseUrl;

    @Value("${ice.api.fields}")
    private String fields;

    private static final String CACHE_DIR = "cr-check-cache";

    // Utility to get cache file path for a CR
    private Path getCacheFilePath(String cr) {
        return Paths.get(CACHE_DIR, cr + ".json");
    }

    // Read cached check response from disk
    private Map<String, Map<String, String>> readCheckCache(String cr) {
        try {
            Path path = getCacheFilePath(cr);
            if (Files.exists(path)) {
                logger.info("Cache hit for CR: {}", cr);
                String json = Files.readString(path);
                ObjectMapper mapper = new ObjectMapper();
                return mapper.readValue(json, Map.class);
            }
        } catch (Exception e) {
            logger.warn("Failed to read cache for CR {}: {}", cr, e.getMessage());
        }
        logger.info("Cache miss for CR: {}", cr);
        return null;
    }

    // Write check response to disk
    private void writeCheckCache(String cr, Map<String, Map<String, String>> response) {
        try {
            Files.createDirectories(Paths.get(CACHE_DIR));
            Path path = getCacheFilePath(cr);
            ObjectMapper mapper = new ObjectMapper();
            Files.writeString(path, mapper.writeValueAsString(response));
            logger.info("Cache updated for CR: {}", cr);
        } catch (Exception e) {
            logger.warn("Failed to write cache for CR {}: {}", cr, e.getMessage());
        }
    }

    public List<ChangeRequest> getChangeRequests(int offset, int limit,
                                                 String afterDate, String beforeDate, List<Integer> appIds,
                                                 String orderDirection, String secretKey) {
        try {
            logger.info("Fetching ChangeRequests from ICE API. Offset: {}, Limit: {}, After: {}, Before: {}, AppIds: {}, Order: {}",
                    offset, limit, afterDate, beforeDate, appIds, orderDirection);
            String appIdsParam = appIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            String apiUrl = String.format(
                    "%s?fields=%s,gsdAssigneeUserStaffID&offset=%d&limit=%d&afterGsdScheduledStartDate=%s&beforeGsdScheduledStartDate=%s&anyGsdAppIDs=%s&orderDirection=%s",
                    baseUrl, fields, offset, limit, afterDate, beforeDate, appIdsParam, orderDirection
            );

            String password = EncryptionUtil.decrypt(encryptedPassword, secretKey);
            String encodedAuth = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());

            HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Authorization", "Basic " + encodedAuth);

            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                String response = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                        .lines().collect(Collectors.joining());
                List<ChangeRequest> changeRequests = ChangeRequestParser.parseChangeRequests(response);

                // Enrich ChangeRequest objects with name and teamName
                for (ChangeRequest cr : changeRequests) {
                    String employeeId = cr.getGsdAssigneeUserStaffID();
                    if (employeeId != null && !employeeId.isEmpty()) {
                        enrichChangeRequestWithEmployeeDetails(cr, employeeId);
                    }
                }
                return changeRequests;
            } else {
                throw new RuntimeException("Failed to fetch data. HTTP Response Code: " + connection.getResponseCode());
            }
        } catch (Exception e) {
            logger.error("Error making API call: {}", e.getMessage(), e);
            throw new RuntimeException("Error making API call: " + e.getMessage(), e);
        }
    }

    private void enrichChangeRequestWithEmployeeDetails(ChangeRequest cr, String employeeId) {
        try {
            String apiUrl = String.format("https://apprunner.hk.hsbc/poddy/people/%s", employeeId);
            HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");

            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                String response = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                        .lines().collect(Collectors.joining());
                JSONObject jsonResponse = new JSONObject(response);

                String name = jsonResponse.optString("name", "");
                String teamName = jsonResponse.optJSONObject("team") != null
                        ? jsonResponse.optJSONObject("team").optString("name", "")
                        : "";

                cr.setName(name);
                cr.setTeamName(teamName);
                logger.info("Enriched ChangeRequest with name: {} and teamName: {}", name, teamName);
            } else {
                logger.warn("Failed to fetch employee details for ID {}. HTTP Response Code: {}", employeeId, connection.getResponseCode());
            }
        } catch (Exception e) {
            logger.error("Error fetching employee details for ID {}: {}", employeeId, e.getMessage(), e);
        }
    }

    public Map<String, Map<String, String>> checkCR(String cr, String secretKey) throws Exception {
        logger.info("Validating CR: {}", cr);
        ChangeRequest changeRequest = getChangeRequest(cr, secretKey);
        String apiKey = EncryptionUtil.decrypt(encryptedApiKey, secretKey);
        return DifyWorkflowRunner.runWorkflow(changeRequest, apiKey);
    }

    public ChangeRequest getChangeRequest(String cr, String secretKey) {
        try {
            logger.info("Fetching ChangeRequest details for CR: {}", cr);
            String apiUrl = String.format("%s/%s", baseUrl, cr);
            String password = EncryptionUtil.decrypt(encryptedPassword, secretKey);
            String encodedAuth = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());

            HttpURLConnection connection = (HttpURLConnection) new URL(apiUrl).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("Authorization", "Basic " + encodedAuth);

            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                String response = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                        .lines().collect(Collectors.joining());
                return ChangeRequestParser.buildChangeRequest(new JSONObject(response));
            } else {
                throw new RuntimeException("Failed to fetch data. HTTP Response Code: " + connection.getResponseCode());
            }
        } catch (Exception e) {
            logger.error("Error making API call: {}", e.getMessage(), e);
            throw new RuntimeException("Error making API call: " + e.getMessage(), e);
        }
    }

    // New method: get list of ChangeRequest objects and their check responses (with caching)
    public List<Map<String, Object>> getChangeRequestsWithCheck(
            int offset, int limit, String afterDate, String beforeDate, List<Integer> appIds,
            String orderDirection, String secretKey, boolean refresh) throws Exception {
        logger.info("Fetching ChangeRequests with check responses. Refresh: {}", refresh);
        List<ChangeRequest> crs = getChangeRequests(offset, limit, afterDate, beforeDate, appIds, orderDirection, secretKey);
        String apiKey = EncryptionUtil.decrypt(encryptedApiKey, secretKey);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (ChangeRequest cr : crs) {
            String crNum = cr.getSnCrNumber();
            Map<String, Map<String, String>> checkResp = null;
            if (!refresh) {
                checkResp = readCheckCache(crNum);
            }
            if (checkResp == null) {
                logger.info("Calling Dify workflow for CR: {}", crNum);
                checkResp = DifyWorkflowRunner.runWorkflow(cr, apiKey);
                writeCheckCache(crNum, checkResp);
            }
            Map<String, Object> result = new HashMap<>();
            result.put("changeRequest", cr);
            result.put("checkResponse", checkResp);
            resultList.add(result);
        }
        logger.info("Completed fetching ChangeRequests with check responses.");
        return resultList;
    }
}
