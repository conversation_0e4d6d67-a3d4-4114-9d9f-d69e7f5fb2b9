package com.hsbc.ssv.bbd.risc.bean;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ChangeRequest {
    private String gsdScheduledStartDate;
    private String gsdBackoutPlan;
    private String gsdFullyApprovedDate;
    private String gsdClosureCode;
    private List<Object> gsdAppIDs;
    private boolean gsdRequiresPar;
    private String snCrNumber;
    private String gsdTechnicalImpact;
    private boolean gsdIsProduction;
    private List<String> requirementUrls;
    private String gsdScheduledEndDate;
    private String gsdChangeType;
    private String gsdBusinessImpact;
    private String gsdImplementationPlan;
    private String gsdTitle;
    private String gsdStatus;
    private String gsdDescription;
    private String artifacts;
    private String gsdTestPlan;
    private String gsdAssigneeUserStaffID;
    private String name;
    private String teamName;

    public String getGsdScheduledStartDate() {
        return gsdScheduledStartDate;
    }

    public void setGsdScheduledStartDate(String gsdScheduledStartDate) {
        this.gsdScheduledStartDate = gsdScheduledStartDate;
    }

    public String getGsdBackoutPlan() {
        return gsdBackoutPlan;
    }

    public void setGsdBackoutPlan(String gsdBackoutPlan) {
        this.gsdBackoutPlan = gsdBackoutPlan;
    }

    public String getGsdFullyApprovedDate() {
        return gsdFullyApprovedDate;
    }

    public void setGsdFullyApprovedDate(String gsdFullyApprovedDate) {
        this.gsdFullyApprovedDate = gsdFullyApprovedDate;
    }

    public String getGsdClosureCode() {
        return gsdClosureCode;
    }

    public void setGsdClosureCode(String gsdClosureCode) {
        this.gsdClosureCode = gsdClosureCode;
    }

    public List<Object> getGsdAppIDs() {
        return gsdAppIDs;
    }

    public void setGsdAppIDs(List<Object> gsdAppIDs) {
        this.gsdAppIDs = gsdAppIDs;
    }

    public boolean isGsdRequiresPar() {
        return gsdRequiresPar;
    }

    public void setGsdRequiresPar(boolean gsdRequiresPar) {
        this.gsdRequiresPar = gsdRequiresPar;
    }

    public String getSnCrNumber() {
        return snCrNumber;
    }

    public void setSnCrNumber(String snCrNumber) {
        this.snCrNumber = snCrNumber;
    }

    public String getGsdTechnicalImpact() {
        return gsdTechnicalImpact;
    }

    public void setTechnicalImpact(String gsdTechnicalImpact) {
        this.gsdTechnicalImpact = gsdTechnicalImpact;
    }

    public boolean isGsdIsProduction() {
        return gsdIsProduction;
    }

    public void setGsdIsProduction(boolean gsdIsProduction) {
        this.gsdIsProduction = gsdIsProduction;
    }

    public List<String> getRequirementUrls() {
        return requirementUrls;
    }

    public void setRequirementUrls(List<String> requirementUrls) {
        this.requirementUrls = requirementUrls;
    }

    public String getGsdScheduledEndDate() {
        return gsdScheduledEndDate;
    }

    public void setGsdScheduledEndDate(String gsdScheduledEndDate) {
        this.gsdScheduledEndDate = gsdScheduledEndDate;
    }

    public String getGsdChangeType() {
        return gsdChangeType;
    }

    public void setGsdChangeType(String gsdChangeType) {
        this.gsdChangeType = gsdChangeType;
    }

    public String getGsdBusinessImpact() {
        return gsdBusinessImpact;
    }

    public void setGsdBusinessImpact(String gsdBusinessImpact) {
        this.gsdBusinessImpact = gsdBusinessImpact;
    }

    public String getGsdImplementationPlan() {
        return gsdImplementationPlan;
    }

    public void setGsdImplementationPlan(String gsdImplementationPlan) {
        if(gsdImplementationPlan == null) return;
        this.gsdImplementationPlan = gsdImplementationPlan.length() > 1000 ? gsdImplementationPlan.substring(0,1000) : gsdImplementationPlan;
    }

    public String getGsdTitle() {
        return gsdTitle;
    }

    public void setGsdTitle(String gsdTitle) {
        this.gsdTitle = gsdTitle;
    }

    public String getGsdStatus() {
        return gsdStatus;
    }

    public void setCRStatus(String gsdStatus) {
        this.gsdStatus = gsdStatus;
    }

    public String getGsdDescription() {
        return gsdDescription;
    }

    public void setDescription(String gsdDescription) {

        this.gsdDescription = gsdDescription.length() > 1000 ? gsdDescription.substring(0,1000) : gsdDescription;
    }
    public String getArtifacts() {
        return artifacts;
    }

    public void setArtifacts(String artifacts) {
        this.artifacts = artifacts;
    }

    public String getGsdTestPlan() {
        return gsdTestPlan;
    }

    public void setPVT(String gsdTestPlan) {
        this.gsdTestPlan = gsdTestPlan;

    }

    public String getGsdAssigneeUserStaffID() {
        return gsdAssigneeUserStaffID;
    }

    public void setGsdAssigneeUserStaffID(String gsdAssigneeUserStaffID) {
        this.gsdAssigneeUserStaffID = gsdAssigneeUserStaffID;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
}
