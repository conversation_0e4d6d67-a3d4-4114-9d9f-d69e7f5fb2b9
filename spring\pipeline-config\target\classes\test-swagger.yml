openapi: 3.0.0
info:
  version: 1.0.0
  title: Pipeline test service
servers:
  - url: http://config-service/api
paths:
  /projects/{projectId}/test/case:
    get:
      summary: Get list of test cases
      operationId: getTestCaseList
      tags:
        - Test Cases
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdsQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/TestTypeQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestCaseResponse'
    put:
      summary: Insert/update test case
      operationId: upsertTestCase
      tags:
        - Test Cases
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestCase'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestCase'
  /projects/{projectId}/test/case/{id}:
    get:
      summary: Get test case
      operationId: getTestCase
      tags:
        - Test Cases
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestCase'
    put:
      summary: Update test case
      operationId: updateTestCase
      tags:
        - Test Cases
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestCase'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestCase'
    delete:
      summary: Delete test case
      operationId: deleteTestCase
      tags:
        - Test Cases
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK

  /projects/{projectId}/test/data:
    get:
      summary: Get list of test data
      operationId: getTestDataList
      tags:
        - Test Data
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/TestDataTypeQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestDataResponse'
    put:
      summary: Insert/update test data
      operationId: upsertTestData
      tags:
        - Test Data
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TestData'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestData'
  /projects/{projectId}/test/data/{id}:
    get:
      summary: Get test data
      operationId: getTestData
      tags:
        - Test Data
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestData'
    put:
      summary: Update test data
      operationId: updateTestData
      tags:
        - Test Data
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TestData'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestData'
    delete:
      summary: Delete test data
      operationId: deleteTestData
      tags:
        - Test Data
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK

  /projects/{projectId}/test/history:
    get:
      summary: Get list of test history
      operationId: getTestHistoryList
      tags:
        - Test History
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/HistoryStatusQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestHistoryResponse'
    put:
      summary: Insert/update pipeline test history
      operationId: upsertTestHistory
      tags:
        - Test History
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestHistory'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestHistory'
  /projects/{projectId}/test/history/{id}:
    get:
      summary: Get test history
      operationId: getTestHistory
      tags:
        - Test History
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestHistory'
    put:
      summary: Update pipeline test history
      operationId: updateTestHistory
      tags:
        - Test History
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TestHistory'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TestHistory'
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    IdsQuery:
      name: ids
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
    NameQuery:
      name: name
      in: query
      schema:
        type: string
    VersionQuery:
      name: version
      in: query
      required: false
      schema:
        type: string
    TestTypeQuery:
      name: type
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/TestCaseType'
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
    TestDataTypeQuery:
      in: query
      name: type
      required: false
      schema:
        $ref: '#/components/schemas/TestDataType'
    HistoryStatusQuery:
      in: query
      name: status
      required: false
      schema:
        $ref: '#/components/schemas/TestHistoryStatus'

  schemas:
    TestHistoryStatus:
      type: string
      enum: [ 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'ERROR', 'TIMED_OUT' ]
    TestDataType:
      type: string
      enum: [ 'JSON', 'XML', 'CSV', 'FIXED_LENGTH', 'SQL' ]
    TestCaseType:
      type: string
      enum: [ 'FUNCTIONAL', 'PERFORMANCE' ]

    TestCaseResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/TestCase'
        total:
          type: integer
          format: int64

    TestCase:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        type:
          $ref: '#/components/schemas/TestCaseType'
        contents:
          type: array
          items:
            $ref: '#/components/schemas/TestCaseContent'

    TestCaseContent:
      type: object
      properties:
        infraProfileId:
          type: string
        sourceChannels:
          type: array
          items:
            $ref: '#/components/schemas/TestSourceChannel'
        dataPersistenceValidations:
          type: array
          items:
            $ref: '#/components/schemas/TestDataPersistence'
        customApiValidations:
          type: array
          items:
            $ref: '#/components/schemas/TestCustomApi'
        consumerChannelValidations:
          type: array
          items:
            $ref: '#/components/schemas/TestConsumerChannel'

    TestSourceChannel:
      type: object
      properties:
        sourceAdaptorId:
          type: string
        testMessageDataList:
          type: array
          items:
            $ref: '#/components/schemas/TestMessageDataList'
        kafkaChannelConfig:
          $ref: '#/components/schemas/TestKafkaSourceChannelConfig'
    TestMessageDataList:
      type: object
      properties:
        testDataId:
          type: string
        messageBulkPublishConfig:
          $ref: '#/components/schemas/MessageBulkPublishConfig'

    MessageBulkPublishConfig:
      type: object
      properties:
        numberOfBatches:
          type: integer
        numberOfMessagesPerBatch:
          type: integer
        pauseInSeconds:
          type: integer

    TestKafkaSourceChannelConfig:
      type: object
      properties:
        topic:
          type: string
        schema:
          type: string

    TestDataPersistence:
      type: object
      properties:
        dataPersistenceId:
          type: string
        validationConfig:
          $ref: '#/components/schemas/ValidationConfig'
        testDataId:
          type: string

    TestCustomApi:
      type: object
      properties:
        endpointId:
          type: string
        idField:
          type: string
        validationConfig:
          $ref: '#/components/schemas/ValidationConfig'
        customApiConfig:
          $ref: '#/components/schemas/TestCustomApiConfig'
        expectedResponseCode:
          type: integer
          format: int64
        expectedResultMessagesTestDataId:
          type: string
        expectedPageInfo:
          $ref: '#/components/schemas/TestCustomApiPageInfo'
        expectedErrorMessage:
          type: string

    TestCustomApiConfig:
      type: object
      properties:
        page:
          type: integer
          format: int64
        queryParams:
          type: object
          additionalProperties:
            type: object
        headers:
          type: object
          additionalProperties:
            type: object

    TestCustomApiPageInfo:
      type: object
      properties:
        nextPageIdx:
          type: integer
          format: int64
        prePageIdx:
          type: integer
          format: int64
        currentPageIdx:
          type: integer
          format: int64
        pageSize:
          type: integer
          format: int64
        totalNumberOfRecords:
          type: integer
          format: int64
        totalPage:
          type: integer
          format: int64


    TestConsumerChannel:
      type: object
      properties:
        consumerAdaptorId:
          type: string
        validationConfig:
          $ref: '#/components/schemas/ValidationConfig'
        testDataIds:
          type: array
          items:
            type: string

    ValidationConfig:
      type: object
      properties:
        idField:
          type: string
        timeout:
          type: integer
          format: int64
        cleanUpData:
          type: boolean


    TestHistoryResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/TestHistory'
        total:
          type: integer
          format: int64
    TestHistory:
      type: object
      properties:
        id:
          type: string
        createdDate:
          type: string
          format: 'date-time'
        versionId:
          type: string
        version:
          type: string
        status:
          $ref: '#/components/schemas/TestHistoryStatus'
        result:
          type: string
    TestDataResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/TestData'
        total:
          type: integer
          format: int64
    TestData:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        type:
          $ref: '#/components/schemas/TestDataType'
        content:
          type: string