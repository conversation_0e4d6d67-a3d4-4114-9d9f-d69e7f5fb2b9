openapi: 3.0.0
info:
  version: 1.0.0
  title: Rollout Status
servers:
  - url: http://config-service/api
paths:
  /projects/{projectId}/versions/{versionId}/rollout-statuses:
    put:
      tags:
        - Rollout Status
      operationId: rolloutStatusPut
      summary: Update/Insert rollout status
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionIdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolloutStatus'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolloutStatus'
    patch:
      tags:
        - Rollout Status
      operationId: rolloutStatusPatch
      summary: Patch rollout status
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionIdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RolloutStatus'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolloutStatus'
components:
  parameters:
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    VersionIdPath:
      name: versionId
      in: path
      required: true
      schema:
        type: string
  schemas:
    RolloutStatus:
      type: object
      properties:
        id:
          type: string
        envType:
          $ref: '#/components/schemas/EnvType'
        envName:
          type: string
        jobId:
          type: string
        jobUrl:
          type: string
        overallStatus:
          type: string
        overallStatusDescription:
          type: string
        overallProgress:
          type: number
        deployments:
          type: array
          items:
            $ref: '#/components/schemas/Deployment'
        createdBy:
          type: string
        createdTime:
          type: string
          format: date-time
    Deployment:
      type: object
      properties:
        name:
          type: string
        status:
          type: string
        statusDescription:
          type: string
