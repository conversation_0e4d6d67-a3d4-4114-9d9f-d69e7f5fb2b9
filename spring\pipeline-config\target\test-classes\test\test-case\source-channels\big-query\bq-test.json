{"pipelineId": "pipelineId", "name": "big query test", "dependantData": [], "postBlendData": [], "sourceChannel": "BIG_QUERY", "infraProfileId": "infraProfileId", "sourceDataFormat": "JSON", "sourceData": [{"name": "sample", "description": "sample item for test", "size": 2, "location": "London", "country": "UK", "item": "Sample item"}, {"name": "Laptop", "description": "Work laptop", "size": 32, "location": "Warsaw", "country": "Poland", "item": "Lenovo laptop"}], "postgreSql": {"dbName": "dbN<PERSON>", "tableName": "bigquery-test", "uniqueFieldName": "name", "transformedData": [{"_id": "SAMPLE", "name": "sample", "description": "sample item for test", "item": "SAMPLE ITEM", "size": "2", "address": "London UK"}, {"_id": "LAPTOP", "name": "Laptop", "description": "Work laptop", "item": "LENOVO LAPTOP", "size": "32", "address": "Warsaw Poland"}]}}