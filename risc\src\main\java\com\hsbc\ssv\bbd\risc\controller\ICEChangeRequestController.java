package com.hsbc.ssv.bbd.risc.controller;

import com.hsbc.ssv.bbd.risc.bean.ChangeRequest;
import com.hsbc.ssv.bbd.risc.service.ICEService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@RestController
@RequestMapping("/crs")
public class ICEChangeRequestController {

    private static final Logger logger = LoggerFactory.getLogger(ICEChangeRequestController.class);

    private final ICEService iceService;

    public ICEChangeRequestController(ICEService iceService) {
        this.iceService = iceService;
    }
    @Value("${ice.api.offset}")
    private int offset;

    @Value("${app.secretKey}")
    private String secretKey;

    @GetMapping("list")
    public List<ChangeRequest> getChangeRequests(
            @RequestParam(defaultValue = "0", required = false) int offset,
            @RequestParam(defaultValue = "1000", required = false) int limit,
            @RequestParam(required = false) String afterDate,
            @RequestParam(required = false) String beforeDate,
            @RequestParam(defaultValue = "11465671", required = false) List<Integer> appIds,
            @RequestParam(defaultValue = "Descending", required = false) String orderDirection) {
        logger.info("API /crs/list called with offset={}, limit={}, afterDate={}, beforeDate={}, appIds={}, orderDirection={}",
                offset, limit, afterDate, beforeDate, appIds, orderDirection);
        // Calculate start of Monday and end of Sunday for the current week
        if (afterDate == null || afterDate.isEmpty()) {
            afterDate = LocalDate.now()
                    .with(DayOfWeek.MONDAY)
                    .atStartOfDay()
                    .atOffset(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"));
        }

        if (beforeDate == null || beforeDate.isEmpty()) {
            beforeDate = LocalDate.now()
                    .with(DayOfWeek.MONDAY)
                    .plusWeeks(1)
                    .atStartOfDay()
                    .atOffset(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"));
        }
        System.out.println(afterDate);
        System.out.println(beforeDate);
        return iceService.getChangeRequests(offset, limit, afterDate, beforeDate, appIds, orderDirection, secretKey);
    }

    @GetMapping("/list-with-check")
    public List<Map<String, Object>> getChangeRequestsWithCheck(
            @RequestParam(defaultValue = "0", required = false) int offset,
            @RequestParam(defaultValue = "1000", required = false) int limit,
            @RequestParam(required = false) String afterDate,
            @RequestParam(required = false) String beforeDate,
            @RequestParam(defaultValue = "11465671", required = false) List<Integer> appIds,
            @RequestParam(defaultValue = "Descending", required = false) String orderDirection,
            @RequestParam(defaultValue = "false", required = false) boolean refresh
    ) throws Exception {
        logger.info("API /crs/list-with-check called with offset={}, limit={}, afterDate={}, beforeDate={}, appIds={}, orderDirection={}, refresh={}",
                offset, limit, afterDate, beforeDate, appIds, orderDirection, refresh);
        // Calculate start of Monday and end of Sunday for the current week
        if (afterDate == null || afterDate.isEmpty()) {
            afterDate = LocalDate.now()
                    .with(DayOfWeek.MONDAY)
                    .atStartOfDay()
                    .atOffset(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"));
        }
        if (beforeDate == null || beforeDate.isEmpty()) {
            beforeDate = LocalDate.now()
                    .with(DayOfWeek.MONDAY)
                    .plusWeeks(1)
                    .atStartOfDay()
                    .atOffset(ZoneOffset.UTC)
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"));
        }
        return iceService.getChangeRequestsWithCheck(offset, limit, afterDate, beforeDate, appIds, orderDirection, secretKey, refresh);
    }

    @GetMapping("/details/{cr}")
    public ChangeRequest getChangeRequestDetails(@PathVariable String cr) {
        logger.info("API /crs/details/{} called", cr);
        return iceService.getChangeRequest(cr, secretKey);
    }

    @GetMapping("/check/{cr}")
    public Map<String, Map<String, String>> checkCR(@PathVariable String cr) throws Exception {
        logger.info("API /crs/check/{} called", cr);
        return iceService.checkCR(cr, secretKey);
    }
}
