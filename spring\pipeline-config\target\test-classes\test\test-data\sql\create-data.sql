SET SCHEMA 'd9ebd1f6_8501_4acd_8b10_0c5ebbf10a8b';

DROP TABLE IF EXISTS "TEST_FRAMEWORK_CUSTOMER";

CREATE TABLE "TEST_FRAMEWORK_CUSTOMER" (
    customer_id text NULL,
    customer_name text NULL,
    deleted_time_stamp timestamp NULL
);

DROP TABLE IF EXISTS "TEST_FRAMEWORK_ORDER";

CREATE TABLE "TEST_FRAMEWORK_ORDER" (
    order_id text NULL,
    customer_id text NULL,
    employee_name text NULL,
    order_date date NULL,
    price numeric NULL,
    deleted_time_stamp timestamp NULL
);

INSERT INTO "TEST_FRAMEWORK_CUSTOMER" (customer_id, customer_name, deleted_time_stamp) VALUES
    ('1', 'Customer A', null),
    ('2', 'Customer B', null),
    ('3', 'Customer C', null);


INSERT INTO "TEST_FRAMEWORK_ORDER" (order_id, customer_id, employee_name, order_date, price, deleted_time_stamp) VALUES
    ('1', '1', 'Employee 1', '2021-09-15', 100, null),
    ('2', '2', 'Employee 2', '2021-09-14', 200, null),
    ('3', '1', 'Employee 3', '2021-09-13', 300, null),
    ('4', '3', 'Employee 4', '2021-09-12', 400, null),
    ('5', '1', 'Employee 5', '2021-09-11', 500, null);