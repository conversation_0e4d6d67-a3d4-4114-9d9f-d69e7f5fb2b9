package com.hsbc.ssv.bbd.risc;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DifyWorkflowRunner {

    private static final Logger logger = LoggerFactory.getLogger(DifyWorkflowRunner.class);
    private static final String API_URL = "http://hkl25079664.hk.hsbc:5008/v1/workflows/run";

    public static Map<String, Map<String, String>> runWorkflow(Object changeRequest, String apiKey) {
        try {
            logger.info("Preparing payload for workflow validation for CR: {}", changeRequest);

            // Prepare JSON payload
            JSONObject payload = new JSONObject();
            payload.put("inputs", new JSONObject().put("CRFields", new ObjectMapper().writeValueAsString(changeRequest)));
            payload.put("response_mode", "blocking");
            payload.put("user", "abc-123");

            // Setup HTTP connection
            HttpURLConnection connection = (HttpURLConnection) new URL(API_URL).openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Authorization", "Bearer " + apiKey);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            // Send payload
            try (OutputStream os = connection.getOutputStream()) {
                os.write(payload.toString().getBytes());
            }
            logger.info("Payload sent to Dify workflow API.");

            // Read response
            int responseCode = connection.getResponseCode();
            InputStream responseStream = (responseCode == HttpURLConnection.HTTP_OK)
                    ? connection.getInputStream()
                    : connection.getErrorStream();

            String responseString = new BufferedReader(new InputStreamReader(responseStream))
                    .lines()
                    .reduce("", (acc, line) -> acc + line);

            logger.debug("Received response from Dify workflow API: {}", responseString);

            // Parse response
            JSONObject jsonResponse = new JSONObject(responseString);
            String text = jsonResponse.getJSONObject("data").getJSONObject("outputs").getString("text");
            String score = text.split("\\n")[0].replace("Score: ", "").trim();
            String suggestions = text.substring(text.indexOf("Suggestions for improvement:") + 27).trim();

            // Build result map
            Map<String, String> suggestionMap = new HashMap<>();
            int index = 0;
            for (String suggestion : suggestions.split("\\n- ")) {
                if (index != 0 && !suggestion.isBlank()) {
                    suggestionMap.put("" + index, suggestion.trim());
                }
                index++;
            }

            Map<String, Map<String, String>> result = new HashMap<>();
            result.put(score, suggestionMap);
            logger.info("Workflow validation completed for CR. Score: {}", score);
            return result;

        } catch (Exception e) {
            logger.error("Error triggering workflow: {}", e.getMessage(), e);
            throw new RuntimeException("Error triggering workflow: " + e.getMessage(), e);
        }
    }
}
