openapi: 3.0.0
info:
  version: 1.0.0
  title: Service profile
servers:
  - url: http://config-service/api
paths:
  /projects/{projectId}/service-profiles:
    get:
      tags:
        - Service profile
      summary: Get service profile
      operationId: getServiceProfiles
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdQuery'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/ServiceTypeQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceProfileResponse'
    put:
      tags:
        - Service profile
      summary: Upsert service profiles
      operationId: upsertServiceProfiles
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ServiceProfile'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ServiceProfile'
components:
  parameters:
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: id
      in: query
      schema:
        type: array
        items:
          type: string
    VersionQuery:
      name: version
      in: query
      schema:
        type: string
    ServiceTypeQuery:
      name: serviceType
      in: query
      schema:
        $ref: '#/components/schemas/InfraServiceType'
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
  schemas:
    ServiceProfileResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ServiceProfile'
        total:
          type: integer
          format: int64
    ServiceProfile:
      type: object
      properties:
        id:
          type: string
        serviceType:
          $ref: '#/components/schemas/InfraServiceType'
        defaultKubernetesProfileId:
          type: string
        kafkaProfileId:
          type: string
        elkProfileId:
          type: string
        databaseProfileIds:
          type: array
          items:
            type: string
    InfraServiceType:
      type: string
      enum: [ ACCESS, BUSINESS_EVENTS, CACHE, CUSTOM_API, DATA_LOOKUP, KAFKA_AUTO_SCALING, PIPELINE_CONSUMER_ADAPTOR, PIPELINE_SIMULATION, PIPELINE_SOURCE_ADAPTOR, TRANSFORM, SITE_SELECTOR, PROMETHEUS, GRAFANA, SUPPORT_TOOLKIT, SUPPORT_DASHBOARD_BACKEND, SUPPORT_DASHBOARD_FRONTEND, ZIPKIN ]

