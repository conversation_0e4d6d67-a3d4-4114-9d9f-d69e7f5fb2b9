spring:
  flyway:
    locations: classpath:db/migration/h2,classpath:db/migration/common
  application:
    name: pipeline-config
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: password
    initialization-mode: never
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8099
          jwk-set-uri: http://localhost:8099/oauth2/jwks
  jpa:
    show-sql: false
    properties:
      hibernate:
        format_sql: true
init.data: false
graphql:
  servlet:
    websocket:
      enabled: false
generation:
  baseDirectory: helm-charts/i15n-helmchart
  generatedDirectory: helm-charts/i15n-helmchart/generated
  testGeneratedDirectory: tests/generated
  pipelineTestCaseDirectory: pipelines
  blendTestCaseDirectory: blends
  customApiTestCaseDirectory: custom-api
  testPropertiesDirectory: properties
  testDataDirectory: test-data
  sourceChannelTestDirectory: source-channels
  consumerChannelTestDirectory: consumer-channels
  blendTestDirectory: blends
  testDataFileDirectory: data
defaultKafkaConfig:
  - env: DEV
    bootstrapServers: hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094
    schemaRegistryUrl: https://hkl20146687.hc.cloud.hk.hsbc:8082/
    sslKeystoreLocation: /hss/apps/certs/unity-microservices.jks
    sslTruststoreLocation: /hss/apps/certs/unity-microservices.ts
  - env: SIT
    bootstrapServers: hkl20091387.hc.cloud.hk.hsbc:9094,hkl20091388.hc.cloud.hk.hsbc:9094,hkl20091390.hc.cloud.hk.hsbc:9094
    schemaRegistryUrl: https://hkl20091387.hc.cloud.hk.hsbc:8082/
    sslKeystoreLocation: /hss/apps/certs/unity-microservices.jks
    sslTruststoreLocation: /hss/apps/certs/unity-microservices.ts
  - env: UAT
    bootstrapServers: hkl20091446.hc.cloud.hk.hsbc:9094,hkl20091447.hc.cloud.hk.hsbc:9094,hkl20091466.hc.cloud.hk.hsbc:9094,hkl20091467.hc.cloud.hk.hsbc:9094
    schemaRegistryUrl: https://hkl20091446.hc.cloud.hk.hsbc:8082/
    sslKeystoreLocation: /hss/apps/certs/unity-microservices.jks
    sslTruststoreLocation: /hss/apps/certs/unity-microservices.ts
  - env: PREPROD
    bootstrapServers: hkl20091446.hc.cloud.hk.hsbc:9094,hkl20091447.hc.cloud.hk.hsbc:9094,hkl20091466.hc.cloud.hk.hsbc:9094,hkl20091467.hc.cloud.hk.hsbc:9094
    schemaRegistryUrl: https://hkl20091446.hc.cloud.hk.hsbc:8082/
    sslKeystoreLocation: /hss/apps/certs/unity-microservices.jks
    sslTruststoreLocation: /hss/apps/certs/unity-microservices.ts
  - env: PROD
    bootstrapServers: hkl20077608.hc.cloud.hk.hsbc:9094,hkl20077609.hc.cloud.hk.hsbc:9094,hkl20077944.hc.cloud.hk.hsbc:9094,hkl20077945.hc.cloud.hk.hsbc:9094
    schemaRegistryUrl: https://hkl20077608.hc.cloud.hk.hsbc:8082/
    sslKeystoreLocation: /hss/apps/certs/unity-microservices.jks
    sslTruststoreLocation: /hss/apps/certs/unity-microservices.ts
defaultELKConfig:
  - env: DEV
    hosts: hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045
  - env: SIT
    hosts: hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045
  - env: UAT
    hosts: hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045
  - env: PREPROD
    hosts: hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045
  - env: PROD
    hosts: hkl20077603.hc.cloud.hk.hsbc:5045,hkl20077604.hc.cloud.hk.hsbc:5045,hkl20077606.hc.cloud.hk.hsbc:5045
git:
  uri: ssh://*****************:8203/unity-i15n-poc/gke-deployment.git
  privateKey: C:\Users\<USER>\.ssh\id_rsa
logging:
  level:
    org:
      springframework:
        security: INFO
reporting:
  sync: false