openapi: 3.0.0
info:
  version: 1.0.0
  title: Transformer service
servers:
  - url: http://config-service/api
paths:
  /projects/{projectId}/transformers:
    get:
      summary: Get transformers
      tags:
        - Transformer
      operationId: getTransformers
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdQuery'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransformerResponse'
    put:
      summary: Insert/update transformers
      tags:
        - Transformer
      operationId: upsertTransformers
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransformerRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransformerResponse'
  /projects/{projectId}/transformers/{id}:
    get:
      summary: Get transformer
      tags:
        - Transformer
      operationId: getTransformer
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transformer'
    put:
      summary: Update transformer
      tags:
        - Transformer
      operationId: updateTransformer
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Transformer'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Transformer'
    delete:
      summary: Delete transformer
      tags:
        - Transformer
      operationId: deleteTransformer
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: id
      in: query
      schema:
        type: array
        items:
          type: string
    VersionQuery:
      name: version
      in: query
      schema:
        type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
    NameQuery:
      in: query
      name: name
      required: false
      schema:
        type: string
    TransformerTypeQuery:
      in: query
      name: type
      required: false
      schema:
        $ref: '#/components/schemas/TransformerType'
  schemas:
    TransformerRequest:
      type: array
      items:
        $ref: '#/components/schemas/Transformer'
    TransformerResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Transformer'
        total:
          type: integer
          format: int64
    Transformer:
      type: object
      properties:
        id:
          type: string
        scriptId:
          type: string
        utilityScriptIds:
          type: array
          items:
            type: string
        name:
          type: string
        content:
          type: string
        type:
          $ref: '#/components/schemas/TransformerType'
        createdTime:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedTime:
          type: string
          format: date-time
        updateBy:
          type: string
    TransformerType:
      type: string
      enum: [SCRIPT, VISUAL]