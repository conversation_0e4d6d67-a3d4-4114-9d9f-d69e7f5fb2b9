query ($projectId: String!) {
    sourceAdaptors(filter: {
        projectId: $projectId
    }) {
        data {
            id
            name
            mode
            sourceChannel
            sourceMetaData {
                dataTerm {
                    name
                }
                eimData{
                    appId
                    appName
                    status
                }
                personalData
                businessContext {
                    lineOfBusinessCode
                    legalEntityCode
                    legalEntityName
                    country
                }
            }
            sourceDataFormat
            sourceDataKeyFields
            convertValuesToString
            orderGuaranteed
            targetTopics
            kubernetesProfile {
                id
            }
            kafkaProfile {
                id
            }
            batchConfig {
                idPattern
                poller {
                    fixedDelay
                    cron
                }
                dependencies {
                    pipelineName
                    idPattern
                }
            }
            routingConfig {
                mode
                customRoutingConfig {
                    routerId
                    routerName
                    routerContent
                    targetTopics
                }
                mappingConfig {
                    key
                    value
                    targetTopic
                }
                defaultTargetTopic
            }
            inputParsingConfig {
                xmlParsingConfig {
                    passSourceInPayload
                    exprList {
                        key
                        value
                    }
                    dataFormatList {
                        key
                        value
                    }
                }
                customParsingConfig {
                    converterId
                    converterName
                    converterContent
                    utilityScriptIds
                }
                csvParsingConfig {
                    headerSectionStartRow
                    dataSectionHeaderNames
                    dataSectionStartRow
                    separator
                    quoteCharacter
                    escapeCharacter
                }
                fixedLengthParsingConfig {
                    dataSectionStartRow
                    headerDefinitions {
                        headerName
                        length
                        dataFormat
                    }
                }
            }
        }
        total
    }
}