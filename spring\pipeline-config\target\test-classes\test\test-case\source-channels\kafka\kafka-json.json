{"pipelineId": "pipelineId", "name": "kafka source - json format", "dependantData": [], "postBlendData": [], "sourceChannel": "KAFKA", "infraProfileId": "infraProfileId", "kafkaConfig": {"topic": "unity2-DEV-test-framework-kafka-json"}, "sourceDataFormat": "JSON", "sourceData": {"id": "1", "data1": "test1", "data2": "test2"}, "postgreSql": {"dbName": "dbN<PERSON>", "tableName": "test-kafka-json", "transformedData": [{"_id": "1", "data": "test1-test2"}]}}