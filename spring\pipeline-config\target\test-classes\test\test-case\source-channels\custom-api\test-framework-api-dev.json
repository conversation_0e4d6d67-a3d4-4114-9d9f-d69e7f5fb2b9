{"name": "test-framework-api-ui - GET - 200 OK", "infraProfileId": "infraProfileId", "postgreSqlConfig": {"dataSetupId": "dataSetupId", "dataCleanupId": "dataCleanupId"}, "customApiConfig": {"method": "GET", "baseUrl": "https://api-testframework-dev-vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc/api/custom/test-framework-api-ui", "queryParams": {"customerNames": "Customer A,Customer B", "employeeNames": "Employee 1,Employee 2,Employee 3,Employee 4"}, "body": null, "expectedResponseCode": 200, "expectedResponse": {"resultMessages": [{"customer_name": "Customer A", "employee_name": "Employee 1", "order_date": "2021-09-15", "price": 100}, {"customer_name": "Customer B", "employee_name": "Employee 2", "order_date": "2021-09-14", "price": 200}, {"customer_name": "Customer A", "employee_name": "Employee 3", "order_date": "2021-09-13", "price": 300}], "pageInfo": {"nextPageIdx": 1, "prePageIdx": 1, "currentPageIdx": 1, "pageSize": 10, "totalNumberOfRecords": 3, "totalPage": 1}}}}