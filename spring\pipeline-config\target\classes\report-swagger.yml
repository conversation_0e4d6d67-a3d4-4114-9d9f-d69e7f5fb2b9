openapi: 3.0.0
info:
  version: 1.0.0
  title: Report service
servers:
  - url: http://config-service/api
paths:
  /report/configured/pipelines:
    get:
      tags:
        - Report
      operationId: getConfiguredPipelines
      summary: Get configured pipelines
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSearchResponse'
  /report/configured/pipelines/summary:
    get:
      tags:
        - Report
      operationId: getConfiguredPipelineSummary
      summary: Get Configured Pipeline Summary
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSummaryResponse'
  /report/configured/pipeline/summary/breakdown:
    get:
      tags:
        - Report
      operationId: getConfiguredPipelineSummaryBreakdown
      summary: Get Configured Pipeline Summary Breakdown
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/BreakdownQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSummaryBreakdownLayer'
  /report/deployed/pipelines:
    get:
      tags:
        - Report
      operationId: getDeployedPipelines
      summary: Get pipelines
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/CountDaysQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSearchResponse'
  /report/deployed/pipelines/deployments:
    get:
      tags:
        - Report
      operationId: getDeployedPipelineDeployments
      summary: Get pipeline deployments
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSearchResponse'
  /report/deployed/pipelines/summary:
    get:
      tags:
        - Report
      operationId: getDeployedPipelineSummary
      summary: Get Pipeline Summary
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSummaryResponse'
  /report/deployed/pipeline/summary/breakdown:
    get:
      tags:
        - Report
      operationId: getDeployedPipelineSummaryBreakdown
      summary: Get Pipeline Summary Breakdown
      parameters:
        - $ref: '#/components/parameters/ProjectNameQuery'
        - $ref: '#/components/parameters/EnvNameQuery'
        - $ref: '#/components/parameters/EnvTypeQuery'
        - $ref: '#/components/parameters/DeploymentTargetQuery'
        - $ref: '#/components/parameters/PipelineNameQuery'
        - $ref: '#/components/parameters/SourceAdaptorChannelQuery'
        - $ref: '#/components/parameters/SourceAdaptorFormatQuery'
        - $ref: '#/components/parameters/TransformerTypeQuery'
        - $ref: '#/components/parameters/PersistenceEnabledQuery'
        - $ref: '#/components/parameters/TargetModeQuery'
        - $ref: '#/components/parameters/TargetChannelQuery'
        - $ref: '#/components/parameters/TargetFormatQuery'
        - $ref: '#/components/parameters/AnyServiceVersionQuery'
        - $ref: '#/components/parameters/ConsolidatedServiceVersionQuery'
        - $ref: '#/components/parameters/BreakdownQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportPipelineSummaryBreakdownLayer'
components:
  parameters:
    ProjectNameQuery:
      name: projectName
      in: query
      required: false
      schema:
        type: string
    EnvNameQuery:
      name: envName
      in: query
      required: false
      schema:
        type: string
    EnvTypeQuery:
      name: envType
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/ReportEnv'
    DeploymentTargetQuery:
      name: deploymentTarget
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/ReportDeploymentTarget'
    PipelineNameQuery:
      name: pipelineName
      in: query
      required: false
      schema:
        type: string
    SourceAdaptorChannelQuery:
      in: query
      name: sourceAdaptorChannel
      required: false
      schema:
        $ref: '#/components/schemas/ReportSourceAdaptorChannel'
    SourceAdaptorFormatQuery:
      in: query
      name: sourceAdaptorDataFormat
      required: false
      schema:
        $ref: '#/components/schemas/ReportSourceAdaptorDataFormat'
    TransformerTypeQuery:
      in: query
      name: transformerType
      required: false
      schema:
        $ref: '#/components/schemas/ReportTransformerType'
    PersistenceEnabledQuery:
      in: query
      name: accessPersistenceEnabled
      required: false
      schema:
        type: boolean
    TargetModeQuery:
      in: query
      name: consumerAdaptorMode
      required: false
      schema:
        $ref: '#/components/schemas/ReportConsumerAdaptorMode'
    TargetChannelQuery:
      in: query
      name: consumerAdaptorChannel
      required: false
      schema:
        $ref: '#/components/schemas/ReportConsumerAdaptorChannel'
    TargetFormatQuery:
      in: query
      name: consumerAdaptorDataFormat
      required: false
      schema:
        $ref: '#/components/schemas/ReportConsumerAdaptorDataFormat'
    AnyServiceVersionQuery:
      name: anyServiceVersion
      in: query
      schema:
        type: string
    ConsolidatedServiceVersionQuery:
      name: consolidatedServiceVersion
      in: query
      schema:
        type: string
    BreakdownQuery:
      in: query
      name: breakdown
      required: true
      schema:
        type: array
        items:
          $ref: '#/components/schemas/ReportPipelineSummaryBreakdownField'
    CountDaysQuery:
      in: query
      name: countDays
      description: Accepted values - 1/7/30
      required: true
      schema:
        type: integer
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
  schemas:
    ReportEnv:
      type: string
      enum: [ 'DEV', 'SIT', 'UAT', 'PREPROD', 'PROD' ]
    ReportDeploymentTarget:
      type: string
      enum: [ 'GKE', 'IKP', 'ALICLOUD', 'MANAGED_GKE', 'KOPS' ]
    ReportSourceAdaptorChannel:
      type: string
      enum: [ 'SFTP', 'MQ', 'KAFKA', 'REST', 'REST_POLL', 'DB', 'KAFKA_AVRO', 'BIG_QUERY', 'ROUTING_SOURCE_ADAPTOR' ]
    ReportSourceAdaptorDataFormat:
      type: string
      enum: [ 'JSON', 'CSV', 'FIX_LENGTH_FLAT_FILE', 'XML', 'CUSTOM' ]
    ReportTransformerType:
      type: string
      enum: [ 'SCRIPT', 'VISUAL' ]
    ReportConsumerAdaptorMode:
      type: string
      enum: [ 'MESSAGE', 'BATCH', 'NONE' ]
    ReportConsumerAdaptorChannel:
      type: string
      enum: [ 'KAFKA', 'SFTP', 'REST', 'BIG_QUERY', 'MQ', 'SOLACE', 'SYMPHONY', 'EMAIL', 'X_MATTER', 'NONE' ]
    ReportConsumerAdaptorDataFormat:
      type: string
      enum: [ 'JSON', 'CSV', 'FIX_LENGTH_FLAT_FILE', 'CUSTOM' ,'XML', 'NONE' ]
    ReportPipelineSearchResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ReportPipeline'
        total:
          type: integer
          format: int64
    ReportPipeline:
      type: object
      properties:
        projectId:
          type: string
        projectName:
          type: string
        versionId:
          type: string
        versionName:
          type: string
        infraId:
          type: string
        envType:
          $ref: '#/components/schemas/ReportEnv'
        deploymentTarget:
          $ref: '#/components/schemas/ReportDeploymentTarget'
        envName:
          type: string
        pipelineId:
          type: string
        pipelineName:
          type: string
        sourceAdaptorName:
          type: string
        sourceAdaptorChannel:
          $ref: '#/components/schemas/ReportSourceAdaptorChannel'
        sourceAdaptorDataFormat:
          $ref: '#/components/schemas/ReportSourceAdaptorDataFormat'
        transformerName:
          type: string
        transformerType:
          $ref: '#/components/schemas/ReportTransformerType'
        accessName:
          type: string
        accessPersistenceEnabled:
          type: boolean
        consumerAdaptors:
          type: array
          items:
            $ref: '#/components/schemas/ReportPipelineConsumerAdaptor'
        sourceAdaptorServiceVersion:
          type: string
        transformerServiceVersion:
          type: string
        accessServiceVersion:
          type: string
        consumerAdaptorServiceVersion:
          type: string
        consolidatedServiceVersion:
          type: string
        action:
          $ref: '#/components/schemas/ReportDeploymentAction'
        deploymentCreationTime:
          type: string
          format: date-time
        versionCreationTime:
          type: string
          format: date-time
        sourceAdaptorMessageCount:
          type: integer
          format: int64
        transformerMessageCount:
          type: integer
          format: int64
        dataPersistenceMessageCount:
          type: integer
          format: int64
    ReportPipelineConsumerAdaptor:
      type: object
      properties:
        consumerAdaptorName:
          type: string
        consumerAdaptorMode:
          $ref: '#/components/schemas/ReportConsumerAdaptorMode'
        consumerAdaptorDataFormat:
          $ref: '#/components/schemas/ReportConsumerAdaptorDataFormat'
        consumerAdaptorChannel:
          $ref: '#/components/schemas/ReportConsumerAdaptorChannel'
        consumerAdaptorMessageCount:
          type: integer
          format: int64
    ReportPipelineSummaryResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ReportPipelineSummary'
        total:
          type: integer
          format: int64
    ReportPipelineSummary:
      type: object
      properties:
        projectName:
          type: string
        envName:
          type: string
        envType:
          $ref: '#/components/schemas/ReportEnv'
        deploymentTarget:
          $ref: '#/components/schemas/ReportDeploymentTarget'
        count:
          type: integer
    ReportPipelineSummaryBreakdownField:
      type: string
      enum:
        - sourceAdaptorChannel
        - sourceAdaptorDataFormat
        - transformerType
        - accessPersistenceEnabled
        - consumerAdaptorMode
        - consumerAdaptorChannel
        - consumerAdaptorDataFormat
        - consolidatedServiceVersion
    ReportDeploymentAction:
      type: string
      enum:
        - UNCHANGED
        - DEPLOY
        - UNDEPLOY
    ReportPipelineSummaryBreakdownLayer:
      type: object
      properties:
        field:
          $ref: '#/components/schemas/ReportPipelineSummaryBreakdownField'
        values:
          type: array
          items:
            $ref: '#/components/schemas/ReportPipelineSummaryBreakdownCount'
    ReportPipelineSummaryBreakdownCount:
      type: object
      properties:
        name:
          type: string
        count:
          type: integer
          format: int64
        next:
          $ref: '#/components/schemas/ReportPipelineSummaryBreakdownLayer'