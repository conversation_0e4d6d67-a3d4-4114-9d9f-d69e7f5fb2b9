{"pipelineId": "pipelineId", "name": "rest test - consumer", "dependantData": [], "postBlendData": [], "sourceChannel": "BIG_QUERY", "infraProfileId": "infraProfileId", "sourceDataFormat": "JSON", "sourceData": [{"name": "sample", "description": "sample item for test", "size": 2, "location": "London", "country": "UK", "item": "Sample item"}, {"name": "Laptop", "description": "Work laptop", "size": 32, "location": "Warsaw", "country": "Poland", "item": "Lenovo laptop"}], "consumerAdaptorConfig": {"consumerAdaptorId": "consumerAdaptorId", "consumerUrl": "https://api.testing-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc/consumer-adaptor-test", "expectedData": [{"name": "sample", "description": "sample item for test", "size": 2, "location": "London", "country": "UK", "item": "Sample item"}, {"name": "Laptop", "description": "Work laptop", "size": 32, "location": "Warsaw", "country": "Poland", "item": "Lenovo laptop"}]}}