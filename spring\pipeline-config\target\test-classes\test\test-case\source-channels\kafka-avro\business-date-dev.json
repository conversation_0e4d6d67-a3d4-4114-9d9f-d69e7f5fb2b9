{"kafkaConfig": {"schema": "kafkaSchema", "topic": "kafkaTopic"}, "sourceChannel": "KAFKA_AVRO", "infraProfileId": "infraProfileId", "sourceDataFormat": "JSON", "sourceData": {"XSMDFL": "", "XSTDFD": "19JAN2022", "XSWDAY": "5", "XSBCYL": "5", "XSTDDT": "20220119", "XSLPDT": "20220118", "XSNPDT": "20220120", "XSADYR": "292", "XSEWKD": "5", "SITE": "xx", "EVENT_TS": "2022-01-19T00:00:00.000000000000", "COMMIT_ID": "00000000000000000000", "REL_REC_NO": "1", "AUDIT_TYPE": "RR", "JOURNAL_CD": "U", "JOB": "", "JOB_NO": "000000", "JOB_USER": "", "FILE_NAME": "UNITY 2.0 TEST FRAMEWORK", "PROGRAM": "", "SERVER_NM": "J3SYSTEM", "USER": "", "JOURNAL_NM": "*N", "LIBRARY": "MNLHUBFP", "MEMBER": "SSDATEP"}, "pipelineId": "pipelineId", "postgreSqlConfig": {"dbName": "dbN<PERSON>", "tableName": "BUSINESS_DATE", "transformedData": [{"_id": "XX", "status": "active", "site": "xx", "current": "2022-01-19", "next": "2022-01-20", "last": "2022-01-18", "event_source": "UNITY 2.0 TEST FRAMEWORK"}]}, "name": "business-date-dev"}