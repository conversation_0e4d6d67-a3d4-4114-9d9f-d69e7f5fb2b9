package com.hsbc.ssv.bbd.risc;

import com.hsbc.ssv.bbd.risc.bean.ChangeRequest;
import com.hsbc.ssv.bbd.risc.util.EncryptionUtil;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

import static com.hsbc.ssv.bbd.risc.DifyWorkflowRunner.runWorkflow;

public class ICEChangeRequestReader {

    private static final String BASE_URL = "https://ice.it.global.hsbc/ice/api/v4/changes";
    private static final String FIELDS = "snCrNumber,gsdTitle,gsdDescription,gsdStatus,gsdChangeType,gsdClosedByStaffID,gsdClosedDate,gsdClosureCode,gsdAppIDs,gsdFullyApprovedDate,gsdScheduledStartDate,gsdScheduledEndDate,gsdBusinessClosed,gsdRequiresPar,gsdIsProduction,independentCodeReviewUrl,businessApprovalUrl,testEvidenceUrl,vulnerabilityCountMedium,vulnerabilityCountHigh,vulnerabilityCountCritical,licenceIssueCount,sastIssueCountMedium,sastIssueCountHigh,sastIssueCountCritical,dastIssueCountMedium,dastIssueCountHigh,dastIssueCountCritical,mastIssueCountMedium,mastIssueCountHigh,mastIssueCountCritical,contIssueCountMedium,contIssueCountHigh,contIssueCountCritical,requirementUrls,artifacts,gsdBackoutPlan,gsdImplementationPlan,gsdTechnicalImpact,gsdBusinessImpact";
    private static final int OFFSET = 0;
    private static final int LIMIT = 1;
    private static final String AFTER_DATE = "2025-05-25T00:00:00.675470Z";
    private static final String BEFORE_DATE = "2025-05-27T00:00:00.675470Z";
    private static final List<Integer> APP_IDS = List.of(11465671); // Example IDs
    private static final String ORDER_DIRECTION = "Descending";

    public static void main(String[] args) throws Exception {
        // Join APP_IDs into a comma-separated string
        String appIdsParam = APP_IDS.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        // Construct the API URL dynamically
        String apiUrl = String.format(
                "%s?fields=%s&offset=%d&limit=%d&afterGsdScheduledStartDate=%s&beforeGsdScheduledStartDate=%s&anyGsdAppIDs=%s&orderDirection=%s",
                BASE_URL, FIELDS, OFFSET, LIMIT, AFTER_DATE, BEFORE_DATE, appIdsParam, ORDER_DIRECTION
        );

        Properties properties = loadProperties();
        if (properties == null) return;

        String username = properties.getProperty("ice.api.username");
        String password = EncryptionUtil.decrypt(properties.getProperty("ice.api.password"), args[0]);
        String apikey = EncryptionUtil.decrypt(properties.getProperty("dify.cr.quality.api"), args[0]);

        try {
            // Make API call
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");

            // Add Basic Auth header
            String encodedAuth = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());
            connection.setRequestProperty("Authorization", "Basic " + encodedAuth);

            // Check response code
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = connection.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                List<ChangeRequest> crs = ChangeRequestParser.parseChangeRequests(response.toString());
                // Parse JSON response
                JSONObject jsonResponse = new JSONObject(response.toString());
                System.out.println("JSON Response: " + jsonResponse.toString(2));

                // Example: Access specific fields
                if (jsonResponse.has("changes")) {
                    JSONArray changes = jsonResponse.getJSONArray("changes");
                    for (int i = 0; i < 1; i++) {
                        JSONObject change = changes.getJSONObject(i);
                        runWorkflow(crs.get(0), apikey);
                    }
                }
            } else {
                System.out.println("Failed to fetch data. HTTP Response Code: " + responseCode);
            }
        } catch (Exception e) {
            System.out.println("Error making API call: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Properties loadProperties() {
        Properties properties = new Properties();
        try (FileInputStream fis = new FileInputStream("src/main/resources/application.properties")) {
            properties.load(fis);
        } catch (IOException e) {
            System.out.println("Error loading properties file: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
        return properties;
    }
}