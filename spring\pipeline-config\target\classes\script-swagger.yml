openapi: 3.0.0
info:
  version: 1.0.0
  title: Script Library service
servers:
  - url: http://config-service/api
paths:
  /projects/{projectId}/scripts:
    get:
      tags:
        - Scripts
      operationId: scriptsGet
      summary: list scripts
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdQuery'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
        - $ref: '#/components/parameters/TypeQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScriptsResponse'
    put:
      tags:
        - Scripts
      operationId: scriptPut
      summary: Update/Insert Script
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Script'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Script'
  /projects/{projectId}/scripts/{id}:
    get:
      tags:
        - Scripts
      operationId: scriptByIdGet
      summary: Get Script by ID
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Script'
    delete:
      tags:
        - Scripts
      operationId: scriptByIdDelete
      summary: Delete Script by ID
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK
  /projects/{projectId}/scripts/clone-from:
    post:
      tags:
        - Scripts
      operationId: cloneScriptFrom
      summary: Clone from another script
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScriptCloneRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Script'
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: id
      in: query
      schema:
        type: array
        items:
          type: string
    NameQuery:
      name: name
      in: query
      schema:
        type: string
    VersionQuery:
      name: version
      in: query
      schema:
        type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
    TypeQuery:
      in: query
      name: type
      required: false
      description: If this field is true, only summary information would be returned
      schema:
        $ref: '#/components/schemas/ScriptType'
  schemas:
    ScriptType:
      type: string
      enum: [ 'SOURCE_ADAPTOR_CONVERTER', 'SOURCE_ADAPTOR_ROUTER',
              'TRANSFORMER', 'TRANSFORMATION_DSL_OPERATION','TRANSFORMATION_DSL_FORMATTER',
              'CONSUMER_ADAPTOR_CONVERTER',
              'CUSTOM_API_QUERY',
              'BUSINESS_EVENT_CONVERTER',
              'UTILITY' ]
    ScriptsResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Script'
        total:
          type: integer
          format: int64
    Script:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        fileName:
          type: string
        description:
          type: string
        type:
          $ref: '#/components/schemas/ScriptType'
        content:
          type: string
        metadata:
          $ref: '#/components/schemas/ScriptMetaData'
        createdTime:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedTime:
          type: string
          format: date-time
        updateBy:
          type: string
    ScriptMetaData:
      type: object
      properties:
        sourceAdaptorRouterDef:
          $ref: '#/components/schemas/ScriptSourceAdaptorRouter'
        transformDslOpDef:
          $ref: '#/components/schemas/ScriptTransformDslOp'
    ScriptSourceAdaptorRouter:
      type: object
      properties:
        targetTopics:
          type: array
          items:
            type: string
    ScriptTransformDslOp:
      type: object
      properties:
        args:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
              description:
                type: string
        isSingleInput:
          type: boolean
    ScriptCloneRequest:
      type: object
      properties:
        sourceProjectId:
          type: string
        sourceScriptId:
          type: string
        newScriptName:
          type: string
