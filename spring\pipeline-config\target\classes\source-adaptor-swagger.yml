openapi: 3.0.0
info:
  version: 1.0.0
  title: Config service
servers:
  - url: http://config-service/api
paths:
  /projects/{projectId}/source-adaptors:
    get:
      summary: Get list of adaptors
      operationId: getSourceAdaptors
      tags:
        - Source Adaptor
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdQuery'
        - $ref: '#/components/parameters/VersionQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/SourceRoutingSourceAdaptorNameQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
        - name: channel
          in: query
          schema:
            $ref: '#/components/schemas/SourceAdaptorChannelEnum'
        - name: format
          in: query
          schema:
            $ref: '#/components/schemas/SourceAdaptorDataFormatEnum'
        - name: mode
          in: query
          schema:
            $ref: '#/components/schemas/SourceAdaptorModeEnum'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceAdaptorResponse'
    put:
      summary: Insert/update source adaptor
      operationId: upsertSourceAdaptor
      tags:
        - Source Adaptor
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/VersionQuery'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SourceAdaptor'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceAdaptor'

  /projects/{projectId}/source-adaptors/{id}:
    get:
      summary: Get source adaptor
      operationId: getSourceAdaptor
      tags:
        - Source Adaptor
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceAdaptor'
    put:
      summary: Update source adaptor
      operationId: updateSourceAdaptor
      tags:
        - Source Adaptor
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SourceAdaptor'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SourceAdaptor'
    delete:
      summary: Delete source adaptor
      operationId: deleteSourceAdaptor
      tags:
        - Source Adaptor
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: id
      in: query
      schema:
        type: array
        items:
          type: string
    NameQuery:
      name: name
      in: query
      schema:
        type: string
    SourceRoutingSourceAdaptorNameQuery:
      name: sourceRoutingSourceAdaptorName
      in: query
      schema:
        type: string
    VersionQuery:
      name: version
      in: query
      schema:
        type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string

  schemas:
    SourceAdaptorResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/SourceAdaptor'
        total:
          type: integer
          format: int64
    SourceAdaptor:
      type: object
      required:
        - name
        - orderGuaranteed
        - sourceChannel
        - sourceDataFormat
        - sourceDataKeyFields
      properties:
        id:
          type: string
        name:
          type: string
        sourceMetaData:
          $ref: '#/components/schemas/SourceAdaptorMetaData'
        sourceChannel:
          $ref: '#/components/schemas/SourceAdaptorChannelEnum'
        sourceDataFormat:
          $ref: '#/components/schemas/SourceAdaptorDataFormatEnum'
        mode:
          $ref: '#/components/schemas/SourceAdaptorModeEnum'
        inputParsingConfig:
          $ref: '#/components/schemas/SourceAdaptorInputParsingConfig'
        additionalProperties:
          type: array
          items:
            $ref: '#/components/schemas/SourceAdaptorAdditionalProperties'
        sourceDataKeyFields:
          type: array
          items:
            type: string
        kafkaProfileId:
          type: string
        kafkaPartitionKeyFields:
          type: array
          items:
            type: string
        idFormatOverride:
          type: string
        convertValuesToString:
          type: boolean
          default: true
        orderGuaranteed:
          type: boolean
          default: false
        routingConfig:
          $ref: '#/components/schemas/SourceAdaptorRoutingConfig'
        batchConfig:
          $ref: '#/components/schemas/SourceAdaptorBatchConfig'
        targetTopics:
          type: array
          description: used to display only
          items:
            type: string
        kubernetesProfileId:
          type: string
        createdTime:
          type: string
          format: date-time
        createdBy:
          type: string
        updatedTime:
          type: string
          format: date-time
        updateBy:
          type: string
    SourceAdaptorChannelEnum:
      type: string
      enum: [ 'SFTP', 'MQ', 'KAFKA', 'REST', 'REST_POLL', 'DB', 'KAFKA_AVRO', 'BIG_QUERY', 'ROUTING_SOURCE_ADAPTOR', 'PUB_SUB' ]
    SourceAdaptorDataFormatEnum:
      type: string
      enum: [ 'JSON', 'CSV', 'FIX_LENGTH_FLAT_FILE', 'XML', 'CUSTOM' ]
    SourceAdaptorModeEnum:
      type: string
      enum: [ 'PIPELINE', 'ROUTING' ]
      default: 'PIPELINE'
    SourceAdaptorRoutingModeEnum:
      type: string
      enum: [ 'CUSTOM', 'MAPPING', 'DEFAULT' ]
      default: 'DEFAULT'
    SourceAdaptorAdditionalProperties:
      type: object
      properties:
        infraProfileId:
          type: string
        mqConfig:
          $ref: '#/components/schemas/SourceAdaptorMqConfig'
        dbConfig:
          $ref: '#/components/schemas/SourceAdaptorDbConfig'
        sftpConfig:
          $ref: '#/components/schemas/SourceAdaptorSftpConfig'
        sourceKafkaConfig:
          $ref: '#/components/schemas/SourceAdaptorSourceKafkaConfig'
        bigQueryConfig:
          $ref: '#/components/schemas/SourceAdaptorBigQueryConfig'
        restPollConfig:
          $ref: '#/components/schemas/SourceAdaptorRestPollConfig'
        sourceAdaptorRefConfig:
          $ref: '#/components/schemas/SourceAdaptorRefConfig'
        restConfig:
          $ref: '#/components/schemas/SourceAdaptorRestConfig'
        pubSubConfig:
          $ref: '#/components/schemas/SourceAdaptorPubSubConfig'

    SourceAdaptorRestConfig:
      type: object
      properties:
        mutualSSLEnabled:
          type: boolean
        cnMatchingRegex:
          type: string
        oauth2Config:
          $ref: '#/components/schemas/SourceAdaptorRestServerOauth2Config'

    SourceAdaptorRestServerOauth2Config:
      type: object
      properties:
        issuerUri:
          type: string
        scope:
          type: string

    SourceAdaptorRefConfig:
      type: object
      properties:
        name:
          type: string
        envName:
          type: string
        topic:
          type: string
    SourceAdaptorInputParsingConfig:
      type: object
      properties:
        csvParsingConfig:
          $ref: '#/components/schemas/CsvParsingConfig'
        xmlParsingConfig:
          $ref: '#/components/schemas/XmlParsingConfig'
        fixedLengthParsingConfig:
          $ref: '#/components/schemas/FixedLengthParsingConfig'
        customParsingConfig:
          $ref: '#/components/schemas/CustomParsingConfig'
    CsvParsingConfig:
      type: object
      properties:
        headerSectionStartRow:
          type: integer
        dataSectionStartRow:
          type: integer
        dataSectionHeaderNames:
          type: array
          items:
            type: string
        separator:
          type: string
          maxLength: 1
        quoteCharacter:
          type: string
          maxLength: 1
        escapeCharacter:
          type: string
          maxLength: 1
    FixedLengthParsingConfig:
      type: object
      properties:
        dataSectionStartRow:
          type: integer
        headerDefinitions:
          type: array
          items:
            $ref: '#/components/schemas/FixedLengthHeaderDefinition'
    FixedLengthHeaderDefinition:
      type: object
      properties:
        headerName:
          type: string
        length:
          type: integer
        dataFormat:
          $ref: '#/components/schemas/FixedLengthDataFormatEnum'
    FixedLengthDataFormatEnum:
      type: string
      enum: [ 'INTEGER', 'TEXT' ]
    XmlParsingConfig:
      type: object
      properties:
        passSourceInPayload:
          type: boolean
          default: false
        exprList:
          type: array
          items:
            $ref: '#/components/schemas/XmlParsingConfigExpr'
        dataFormatList:
          type: array
          items:
            $ref: '#/components/schemas/XmlParsingConfigDataFormat'
    CustomParsingConfig:
      type: object
      properties:
        converterId:
          type: string
        converterName:
          type: string
        converterContent:
          type: string
        utilityScriptIds:
          type: array
          items:
            type: string

    SourceAdaptorMqConfig:
      type: object
      properties:
        host:
          type: string
        port:
          type: integer
        channel:
          type: string
        queueManager:
          type: string
        cipherSuite:
          type: string
        appName:
          type: string
        queueName:
          type: string
        keyStore:
          type: string
        keyPassword:
          type: string

    SourceAdaptorDbConfig:
      type: object
      properties:
        url:
          type: string
        query:
          type: string

    SourceAdaptorSftpConfig:
      type: object
      properties:
        host:
          type: string
        port:
          type: integer
        userName:
          type: string
        password:
          type: string
        privateKey:
          type: string
        remoteDirectory:
          type: string
        archiveDirectory:
          type: string
        fileNamePattern:
          type: string

    SourceAdaptorBigQueryConfig:
      type: object
      properties:
        projectId:
          type: string
        datasetName:
          type: string
        tableName:
          type: string
        kmsKeyName:
          type: string
        queryFields:
          type: array
          items:
            type: string

    SourceAdaptorPubSubConfig:
      type: object
      properties:
        projectId:
          type: string
        subscriptionName:
          type: string
        sourceTopic:
          type: string
        location:
          type: string
        kmsKeyName:
          type: string

    SourceAdaptorRestPollConfig:
      type: object
      properties:
        url:
          type: string
        httpMethod:
          $ref: '#/components/schemas/HttpMethod'
        oauth2Config:
          $ref: '#/components/schemas/SourceAdaptorRestOauth2Config'
        basicAuthConfig:
          $ref: '#/components/schemas/SourceAdaptorBasicAuthConfig'
        payload:
          type: string
        limit:
          type: integer
        proxyConfig:
          $ref: '#/components/schemas/SourceAdaptorRestProxyConfig'

    SourceAdaptorRestOauth2Config:
      type: object
      properties:
        url:
          type: string
        scopes:
          type: string
        grantType:
          type: string
        clientId:
          type: string
        clientSecret:
          type: string
        username:
          type: string
        password:
          type: string

    SourceAdaptorBasicAuthConfig:
      type: object
      properties:
        username:
          type: string
        password:
          type: string
    
    SourceAdaptorRestProxyConfig:
      type: object
      properties:
        proxyUrl:
          type: string
        proxyPort:
          type: string

    SourceAdaptorSourceKafkaConfig:
      type: object
      properties:
        sourceTopics:
          type: array
          items:
            $ref: '#/components/schemas/SourceAdaptorKafkaTopic'
        sourceTopic:
          type: string

    SourceAdaptorSourceKafkaProperty:
      type: object
      properties:
        key:
          type: string
        value:
          type: string

    SourceAdaptorKafkaTopic:
      type: object
      properties:
        topicPrefix:
          type: string
        topicSuffix:
          type: string
        deploymentStrategy:
          $ref: '#/components/schemas/deploymentStrategyEnum'
    deploymentStrategyEnum:
      type: string
      enum: [ 'STANDALONE', 'CONSOLIDATE' ]
      default: 'STANDALONE'
    SourceAdaptorAvroSchema:
      type: object
      properties:
        keyFields:
          type: array
          items:
            type: string
        dataFields:
          type: array
          items:
            type: string

    XmlParsingConfigExpr:
      type: object
      properties:
        key:
          type: string
        value:
          type: string

    XmlParsingConfigDataFormat:
      type: object
      properties:
        key:
          type: string
        value:
          $ref: '#/components/schemas/XmlDataFormatEnum'
    XmlDataFormatEnum:
      type: string
      enum: [ 'LIST', 'TEXT' ]
      default: 'TEXT'

    SourceAdaptorRoutingConfig:
      type: object
      properties:
        mode:
          $ref: '#/components/schemas/SourceAdaptorRoutingModeEnum'
        customRoutingConfig:
          $ref: '#/components/schemas/SourceAdaptorCustomRoutingConfig'
        mappingConfig:
          type: array
          items:
            $ref: '#/components/schemas/SourceAdaptorMappingConfig'
        defaultTargetTopic:
          type: string
    SourceAdaptorCustomRoutingConfig:
      type: object
      properties:
        routerId:
          type: string
        routerName:
          type: string
        routerContent:
          type: string
        targetTopics:
          type: array
          items:
            type: string
    SourceAdaptorMappingConfig:
      type: object
      properties:
        key:
          type: string
        value:
          type: string
        targetTopic:
          type: string
    SourceAdaptorBatchConfig:
      type: object
      properties:
        idPattern:
          type: string
          description: Batch ID pattern, supports placeholder {uuid} and {yyyyMMdd}
          default: '${uuid}'
        poller:
          type: object
          description: contains cron or fixedDelay exclusively
          properties:
            fixedDelay:
              type: integer
              description: millisecond
            cron:
              type: string
            timeZone:
              type: string
        dependencies:
          type: array
          items:
            type: object
            properties:
              pipelineName:
                type: string
              idPattern:
                type: string
    SourceAdaptorMetaData:
      type: object
      properties:
        eimData:
          $ref: 'eim-swagger.yml/#/components/schemas/Eim'
        businessContext:
          type: array
          items:
            $ref: 'business-context-swagger.yml/#/components/schemas/BusinessContext'
        dataTerm:
          $ref: 'data-term-swagger.yml/#/components/schemas/DataTerm'
        personalData:
          type: boolean
    HttpMethod:
      type: string
      enum: [ 'POST','GET','PUT','PATCH', 'DELETE' ]