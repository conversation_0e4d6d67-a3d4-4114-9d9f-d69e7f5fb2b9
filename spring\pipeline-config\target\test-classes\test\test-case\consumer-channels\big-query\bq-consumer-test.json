{"pipelineId": "pipelineId", "name": "big query test - consumer", "dependantData": [], "postBlendData": [], "sourceChannel": "BIG_QUERY", "infraProfileId": "infraProfileId", "sourceDataFormat": "JSON", "sourceData": [{"name": "sample", "description": "sample item for test", "size": 2, "location": "London", "country": "UK", "item": "Sample item"}, {"name": "Laptop", "description": "Work laptop", "size": 32, "location": "Warsaw", "country": "Poland", "item": "Lenovo laptop"}], "consumerAdaptorConfig": {"consumerAdaptorId": "consumerAdaptorId", "uniqueFieldName": "_id", "expectedData": [{"name": "sample", "description": "sample item for test", "size": 2, "location": "London", "country": "UK", "item": "Sample item"}, {"name": "Laptop", "description": "Work laptop", "size": 32, "location": "Warsaw", "country": "Poland", "item": "Lenovo laptop"}]}}