import com.poc.hss.fasttrack.custom.CustomApiHandler
import com.poc.hss.fasttrack.custom.CustomApiHandlerContext
import com.poc.hss.fasttrack.model.CustomQueryResponse

class CustomQuery extends CustomApiHandler {

    @Override
    CustomQueryResponse query(CustomApiHandlerContext context) {
        def result = jdbcTemplate.queryForList("select a from b")
        result.get(0).put("jwt", context.getHeaders().get("jwt").toUpperCase())
        result.get(0).put("foo", context.getQueryParams().get("foo"))
        return CustomQueryResponse.builder()
                .resultMessages(result)
                .pageInfo(CustomQueryResponse.PageInfo.builder()
                        .pageSize(5)
                        .totalPage(4)
                        .totalNumberOfRecords(20)
                        .build()
                )
                .build()
    }
}