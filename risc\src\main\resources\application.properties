spring.application.name=risc
server.servlet.context-path=/risc
ice.api.username=ssv-bbd-ice-user
ice.api.password=xARvOECg6EE7/Mnk9T/EUeU9Z3kaFYjxgDlzBmNTzmUILiJD8BSoFYfw2aIu2xxj
dify.cr.quality.api=Tx66zqIAbIDAicSEU3E82BoRa+aNjk/+Xgc4Z6kSsAU=


ice.api.base-url=https://ice.it.global.hsbc/ice/api/v4/changes
ice.api.fields=snCrNumber,gsdTitle,gsdDescription,gsdStatus,gsdChangeType,gsdClosedByStaffID,gsdClosedDate,gsdClosureCode,gsdAppIDs,gsdFullyApprovedDate,gsdScheduledStartDate,gsdScheduledEndDate,gsdBusinessClosed,gsdRequiresPar,gsdIsProduction,independentCodeReviewUrl,businessApprovalUrl,testEvidenceUrl,vulnerabilityCountMedium,vulnerabilityCountHigh,vulnerabilityCountCritical,licenceIssueCount,sastIssueCountMedium,sastIssueCountHigh,sastIssueCountCritical,dastIssueCountMedium,dastIssueCountHigh,dastIssueCountCritical,mastIssueCountMedium,mastIssueCountHigh,mastIssueCountCritical,contIssueCountMedium,contIssueCountHigh,contIssueCountCritical,requirementUrls,artifacts,gsdBackoutPlan,gsdImplementationPlan,gsdTechnicalImpact,gsdBusinessImpact
ice.api.offset=0
ice.api.limit=1000
ice.api.after-date=2025-05-26T00:00:00.675470Z
ice.api.before-date=2025-05-31T00:00:00.675470Z
ice.api.app-ids=11465671
ice.api.order-direction=Descending