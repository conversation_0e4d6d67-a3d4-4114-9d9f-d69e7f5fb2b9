query ($projectId: String!) {
    blends(filter: {
        projectId: $projectId
    }) {
        data {
            id
            name
            transformers {
                id
                name
            }
            sourceAccessSchemas {
                accessSchema {
                    id
                    name
                }
                type
            }
            targetAccessSchema {
                id
                name
            }
            persistenceEnabled
        }
        total
    }
}