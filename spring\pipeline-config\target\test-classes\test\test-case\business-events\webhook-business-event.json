{"businessEventId": "businessEventId", "name": "business event - webhook", "dependantData": [], "postBlendData": [], "sourceChannel": "KAFKA", "infraProfileId": "infraProfileId", "kafkaConfig": {"topic": "unity2-DEV-test-framework-webhook-business-events"}, "sourceDataFormat": "JSON", "sourceData": {"id": "1", "data1": "test1", "data2": "test2"}, "businessEventConfig": {"consumerAdaptorId": "consumerAdaptorId", "consumerUrl": "https://api.testing-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc/webhook", "expectedData": [{"name": "sample", "description": "sample item for test", "size": 2, "location": "London", "country": "UK", "item": "Sample item"}, {"name": "Laptop", "description": "Work laptop", "size": 32, "location": "Warsaw", "country": "Poland", "item": "Lenovo laptop"}]}}