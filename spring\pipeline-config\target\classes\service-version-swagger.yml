openapi: 3.0.0
info:
  version: 1.0.0
  title: Service version service
servers:
  - url: http://config-service/api
paths:
  /service-versions:
    get:
      tags:
        - Service version
      summary: Get service versions
      operationId: getServiceVersions
      parameters:
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceVersionResponse'
components:
  parameters:
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: _id:desc
      schema:
        type: string
  schemas:
    ServiceVersionResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/ServiceVersion'
        total:
          type: integer
          format: int64
    ServiceVersion:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        supported:
          type: boolean
