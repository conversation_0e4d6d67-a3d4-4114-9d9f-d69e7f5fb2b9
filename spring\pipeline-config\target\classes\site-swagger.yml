openapi: 3.0.0
info:
  version: 1.0.0
  title: Site service
servers:
  - url: http://config-service/api
paths:
  /sites:
    get:
      tags:
        - Site
      summary: Get sites
      operationId: getSites
      parameters:
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
        - in: query
          name: name
          schema:
            type: string
        - in: query
          name: projectTopicPrefix
          schema:
            type: string
        - in: query
          name: url
          schema:
            type: string
        - in: query
          name: available
          schema:
            type: boolean
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SiteResponse'
  /sites/{id}:
    post:
      tags:
        - Site
      summary: Update site
      operationId: updateSite
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Site'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Site'
components:
  parameters:
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: _id:desc
      schema:
        type: string
  schemas:
    SiteResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Site'
        total:
          type: integer
          format: int64
    Site:
      type: object
      properties:
        id:
          type: string
        topicPrefix:
          type: string
        name:
          type: string
        env:
          type: string
          enum: [ 'DEV', 'SIT', 'UAT', 'PREPROD', 'PROD']
